body {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
    margin: 0;
    background-color: #f4f4f5;
    color: #303133;
    line-height: 1.5;
}

#app {
    /* Vue app root */
}

.app-header {
    background-color: #409EFF; /* Element UI primary color */
    color: white;
    text-align: center;
    padding: 0 20px; /* Ensure padding doesn't shrink header */
    height: 60px;
    line-height: 60px;
    flex-shrink: 0; /* Prevent header from shrinking */
}

.app-header h1 {
    margin: 0;
    font-size: 1.8em;
    font-weight: 500;
}

.el-main {
    padding: 20px; /* Default padding for main content area */
}

.card-container {
    /* el-row with gutter handles spacing between cards */
}

.card-col {
    margin-bottom: 20px; /* Space between rows of cards on smaller screens */
}

.endorsement-card .el-card__header {
    padding: 12px 15px;
    background-color: rgba(0,0,0,0.02); /* Slightly darker header for contrast */
    font-weight: 600;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.endorsement-card .el-card__body {
    padding: 15px;
    flex-grow: 1; /* Allow body to grow if card is flex container */
}

.instruction-item {
    display: block;
    padding: 10px 12px;
    margin-bottom: 10px;
    border: 1px solid #dcdfe6; /* Element UI default border color */
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
    font-size: 14px;
    color: #606266; /* Element UI secondary text color */
    text-align: left;
    word-wrap: break-word; /* Ensure long instructions wrap */
}

.instruction-item:last-child {
    margin-bottom: 0;
}

.instruction-item:hover,
.instruction-item:focus {
    background-color: #ecf5ff; /* Element UI light blue for hover */
    border-color: #b3d8ff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    outline: none; /* Remove default focus outline if custom styles are sufficient */
}
.instruction-item:focus { /* Add a subtle focus ring for accessibility */
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}


.instruction-item.highlight {
    background-color: #fdfd96; /* Soft yellow for highlight */
    border-color: #fadf5a;
    animation: highlight-pulse 0.35s ease-out;
}

@keyframes highlight-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

.app-footer {
    text-align: center;
    padding: 15px 0;
    background-color: #f8f9fa;
    color: #6c757d;
    font-size: 0.9em;
    border-top: 1px solid #e9ecef;
    flex-shrink: 0; /* Prevent footer from shrinking */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .app-header h1 {
        font-size: 1.5em;
    }
    .el-main {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .app-header {
        height: 50px;
        line-height: 50px;
    }
    .app-header h1 {
        font-size: 1.25em;
    }
    .el-main {
        padding: 10px;
    }
    .instruction-item {
        font-size: 13px;
        padding: 8px 10px;
    }
    .endorsement-card .el-card__header {
        padding: 10px 12px;
    }
    .endorsement-card .el-card__body {
        padding: 10px;
    }
}